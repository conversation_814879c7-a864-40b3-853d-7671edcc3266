using FluentAssertions;
using FluentValidation;
using FluentValidation.Results;
using HighCapital.AuthenticationService.Application.Common.Interfaces;
using HighCapital.AuthenticationService.Service.Controllers.Auth;
using Microsoft.AspNetCore.Mvc;
using Moq;
using NUnit.Framework;

namespace HighCapital.AuthenticationService.Application.UnitTests.Controllers;

[TestFixture]
public class AuthControllerTests
{
    private Mock<IUserService> _mockUserService;
    private Mock<IValidator<HighCapital.AuthenticationService.Application.Validators.RegisterUserRequest>> _mockRegisterValidator;
    private Mock<IValidator<HighCapital.AuthenticationService.Application.Validators.LoginUserRequest>> _mockLoginValidator;
    private AuthController _controller;

    [SetUp]
    public void SetUp()
    {
        _mockUserService = new Mock<IUserService>();
        _mockRegisterValidator = new Mock<IValidator<HighCapital.AuthenticationService.Application.Validators.RegisterUserRequest>>();
        _mockLoginValidator = new Mock<IValidator<HighCapital.AuthenticationService.Application.Validators.LoginUserRequest>>();

        _controller = new AuthController(
            _mockUserService.Object,
            _mockRegisterValidator.Object,
            _mockLoginValidator.Object);
    }

    [Test]
    public async Task Register_WhenValidRequest_ShouldReturnOkWithUserId()
    {
        // Arrange
        var request = new HighCapital.AuthenticationService.Application.Validators.RegisterUserRequest
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            Password = "password123"
        };

        const int expectedUserId = 123;
        var validationResult = new ValidationResult();

        _mockRegisterValidator
            .Setup(v => v.ValidateAsync(request, default))
            .ReturnsAsync(validationResult);

        _mockUserService
            .Setup(s => s.RegisterUserAsync("John", "Doe", "<EMAIL>", "password123", default))
            .ReturnsAsync(HighCapital.AuthenticationService.Application.Common.Models.Result<int>.Success(expectedUserId));

        // Act
        var result = await _controller.Register(request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        okResult.Value.Should().BeEquivalentTo(new { message = "User registered successfully", userId = expectedUserId });
    }

    [Test]
    public async Task Register_WhenInvalidRequest_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new HighCapital.AuthenticationService.Application.Validators.RegisterUserRequest
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "invalid-email",
            Password = "123"
        };

        var validationFailure = new ValidationFailure("Email", "Invalid email format");
        var validationResult = new ValidationResult(new[] { validationFailure });

        _mockRegisterValidator
            .Setup(v => v.ValidateAsync(request, default))
            .ReturnsAsync(validationResult);

        // Act
        var result = await _controller.Register(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().BeEquivalentTo(new { errors = new[] { "Invalid email format" } });
    }

    [Test]
    public async Task Register_WhenServiceReturnsFailure_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new HighCapital.AuthenticationService.Application.Validators.RegisterUserRequest
        {
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>",
            Password = "password123"
        };

        var validationResult = new ValidationResult();
        const string expectedError = "User already exists";

        _mockRegisterValidator
            .Setup(v => v.ValidateAsync(request, default))
            .ReturnsAsync(validationResult);

        _mockUserService
            .Setup(s => s.RegisterUserAsync("John", "Doe", "<EMAIL>", "password123", default))
            .ReturnsAsync(HighCapital.AuthenticationService.Application.Common.Models.Result<int>.Failure(expectedError));

        // Act
        var result = await _controller.Register(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().BeEquivalentTo(new { errors = new[] { expectedError } });
    }

    [Test]
    public async Task Login_WhenValidCredentials_ShouldReturnOkWithToken()
    {
        // Arrange
        var request = new HighCapital.AuthenticationService.Application.Validators.LoginUserRequest
        {
            Email = "<EMAIL>",
            Password = "password123"
        };

        const string expectedToken = "jwt-token";
        var validationResult = new ValidationResult();

        _mockLoginValidator
            .Setup(v => v.ValidateAsync(request, default))
            .ReturnsAsync(validationResult);

        _mockUserService
            .Setup(s => s.LoginUserAsync("<EMAIL>", "password123", default))
            .ReturnsAsync(HighCapital.AuthenticationService.Application.Common.Models.Result<string>.Success(expectedToken));

        // Act
        var result = await _controller.Login(request);

        // Assert
        result.Should().BeOfType<OkObjectResult>();
        var okResult = (OkObjectResult)result;
        okResult.Value.Should().BeEquivalentTo(new { token = expectedToken });
    }

    [Test]
    public async Task Login_WhenInvalidRequest_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new HighCapital.AuthenticationService.Application.Validators.LoginUserRequest
        {
            Email = "invalid-email",
            Password = ""
        };

        var validationFailure = new ValidationFailure("Email", "Invalid email format");
        var validationResult = new ValidationResult(new[] { validationFailure });

        _mockLoginValidator
            .Setup(v => v.ValidateAsync(request, default))
            .ReturnsAsync(validationResult);

        // Act
        var result = await _controller.Login(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().BeEquivalentTo(new { errors = new[] { "Invalid email format" } });
    }

    [Test]
    public async Task Login_WhenInvalidCredentials_ShouldReturnBadRequest()
    {
        // Arrange
        var request = new HighCapital.AuthenticationService.Application.Validators.LoginUserRequest
        {
            Email = "<EMAIL>",
            Password = "wrongpassword"
        };

        var validationResult = new ValidationResult();
        const string expectedError = "Invalid credentials";

        _mockLoginValidator
            .Setup(v => v.ValidateAsync(request, default))
            .ReturnsAsync(validationResult);

        _mockUserService
            .Setup(s => s.LoginUserAsync("<EMAIL>", "wrongpassword", default))
            .ReturnsAsync(HighCapital.AuthenticationService.Application.Common.Models.Result<string>.Failure(expectedError));

        // Act
        var result = await _controller.Login(request);

        // Assert
        result.Should().BeOfType<BadRequestObjectResult>();
        var badRequestResult = (BadRequestObjectResult)result;
        badRequestResult.Value.Should().BeEquivalentTo(new { errors = new[] { expectedError } });
    }
}
