apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: authentication-service

resources:
- k8s/base

images:
- name: gcr.io/PROJECT_ID/IMAGE
  newName: gcr.io/PROJECT_ID/authentication-service
  newTag: TAG

commonLabels:
  app: authentication-service
  managed-by: kustomize



configMapGenerator:
- name: authentication-service-env-config
  literals:
  - BUILD_DATE=$(date)
  - GIT_COMMIT=$(git rev-parse HEAD)

generatorOptions:
  disableNameSuffixHash: true
