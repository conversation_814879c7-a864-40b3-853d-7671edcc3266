apiVersion: v1
kind: ConfigMap
metadata:
  name: authentication-service-config
  namespace: authentication-service
  labels:
    app: authentication-service
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  ASPNETCORE_URLS: "http://+:8080"
  # Database configuration
  DatabaseProvider: "PostgreSQL"
  # Logging configuration
  Logging__LogLevel__Default: "Information"
  Logging__LogLevel__Microsoft.AspNetCore: "Warning"
  # JWT configuration
  Jwt__Issuer: "HighCapital.AuthenticationService"
  Jwt__Audience: "HighCapital.API"
  # CORS configuration
  AllowedOrigins: "*"
  # Health check configuration
  HealthChecks__UI__HealthChecksUri: "/health"
