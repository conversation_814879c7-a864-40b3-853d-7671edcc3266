apiVersion: v1
kind: Secret
metadata:
  name: authentication-service-secret
  namespace: authentication-service
  labels:
    app: authentication-service
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  # To encode: echo -n "your-value" | base64
  ConnectionStrings__DefaultConnection: ""  # PostgreSQL connection string
  Jwt__SecretKey: ""  # JWT secret key
  # Database credentials
  DB_PASSWORD: ""
  # External service API keys
  EXTERNAL_API_KEY: ""
---
# Example of how to create the secret with kubectl:
# kubectl create secret generic authentication-service-secret \
#   --namespace=authentication-service \
#   --from-literal=ConnectionStrings__DefaultConnection="Host=postgres;Database=authdb;Username=authuser;Password=yourpassword" \
#   --from-literal=Jwt__SecretKey="your-super-secret-jwt-key-here" \
#   --from-literal=DB_PASSWORD="yourpassword" \
#   --from-literal=EXTERNAL_API_KEY="your-external-api-key"
