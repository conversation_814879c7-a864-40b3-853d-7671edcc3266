apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: authentication-service-ingress
  namespace: authentication-service
  labels:
    app: authentication-service
  annotations:
    kubernetes.io/ingress.class: "gce"
    kubernetes.io/ingress.global-static-ip-name: "authentication-service-ip"
    networking.gke.io/managed-certificates: "authentication-service-ssl-cert"
    kubernetes.io/ingress.allow-http: "false"
    # Enable HTTPS redirect
    ingress.gcp.kubernetes.io/force-ssl-redirect: "true"
    # Backend configuration for health checks
    cloud.google.com/backend-config: '{"default": "authentication-service-backendconfig"}'
spec:
  rules:
  - host: auth.yourdomain.com  # Replace with your domain
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: authentication-service
            port:
              number: 80
---
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: authentication-service-ssl-cert
  namespace: authentication-service
spec:
  domains:
    - auth.yourdomain.com  # Replace with your domain
---
apiVersion: cloud.google.com/v1
kind: BackendConfig
metadata:
  name: authentication-service-backendconfig
  namespace: authentication-service
spec:
  healthCheck:
    checkIntervalSec: 10
    timeoutSec: 5
    healthyThreshold: 1
    unhealthyThreshold: 3
    type: HTTP
    requestPath: /health
    port: 8080
  timeoutSec: 30
  connectionDraining:
    drainingTimeoutSec: 60
