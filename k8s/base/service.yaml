apiVersion: v1
kind: Service
metadata:
  name: authentication-service
  namespace: authentication-service
  labels:
    app: authentication-service
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: authentication-service
---
apiVersion: v1
kind: Service
metadata:
  name: authentication-service-nodeport
  namespace: authentication-service
  labels:
    app: authentication-service
spec:
  type: NodePort
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
    nodePort: 30080
  selector:
    app: authentication-service
