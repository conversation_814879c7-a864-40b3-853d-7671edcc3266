apiVersion: apps/v1
kind: Deployment
metadata:
  name: authentication-service
  namespace: authentication-service
  labels:
    app: authentication-service
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: authentication-service
  template:
    metadata:
      labels:
        app: authentication-service
        version: v1
    spec:
      containers:
      - name: authentication-service
        image: gcr.io/PROJECT_ID/authentication-service:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: authentication-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: authentication-service-config
              key: ASPNETCORE_URLS
        - name: ConnectionStrings__DefaultConnection
          valueFrom:
            secretKeyRef:
              name: authentication-service-secret
              key: ConnectionStrings__DefaultConnection
        - name: Jwt__SecretKey
          valueFrom:
            secretKeyRef:
              name: authentication-service-secret
              key: Jwt__SecretKey
        - name: Jwt__Issuer
          valueFrom:
            configMapKeyRef:
              name: authentication-service-config
              key: Jwt__Issuer
        - name: Jwt__Audience
          valueFrom:
            configMapKeyRef:
              name: authentication-service-config
              key: Jwt__Audience
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
      securityContext:
        fsGroup: 2000
      restartPolicy: Always
