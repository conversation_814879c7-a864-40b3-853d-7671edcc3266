services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:8080"
    depends_on:
      - postgres
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__HighCapital.AuthenticationServiceDb=Host=postgres;Database=HighCapital.AuthenticationServiceDb;Username=postgres;Password=postgres123
      - SKIP_DB_INIT=false
    networks:
      - app-network

  postgres:
    image: postgres:16-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=HighCapital.AuthenticationServiceDb
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
