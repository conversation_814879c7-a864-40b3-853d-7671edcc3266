# Deployment Guide - Authentication Service

Este guia explica como configurar e fazer deploy do Authentication Service na Google Cloud Platform (GCP) usando Kubernetes.

## Pré-requisitos

### Ferramentas necessárias
- [Google Cloud SDK](https://cloud.google.com/sdk/docs/install)
- [kubectl](https://kubernetes.io/docs/tasks/tools/)
- [Docker](https://docs.docker.com/get-docker/)
- [Skaffold](https://skaffold.dev/docs/install/) (opcional, para desenvolvimento)

### Configuração da GCP

1. **Criar projeto na GCP**
```bash
gcloud projects create YOUR_PROJECT_ID
gcloud config set project YOUR_PROJECT_ID
```

2. **Habilitar APIs necessárias**
```bash
gcloud services enable container.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable cloudbuild.googleapis.com
```

3. **Criar cluster GKE**
```bash
gcloud container clusters create authentication-service-cluster \
    --zone=us-central1-a \
    --num-nodes=3 \
    --enable-autoscaling \
    --min-nodes=1 \
    --max-nodes=10 \
    --machine-type=e2-medium \
    --enable-autorepair \
    --enable-autoupgrade
```

4. **Configurar kubectl**
```bash
gcloud container clusters get-credentials authentication-service-cluster --zone=us-central1-a
```

## Configuração dos Secrets

### 1. Criar secrets do Kubernetes
```bash
kubectl create secret generic authentication-service-secret \
  --namespace=authentication-service \
  --from-literal=ConnectionStrings__DefaultConnection="Host=your-postgres-host;Database=authdb;Username=authuser;Password=yourpassword" \
  --from-literal=Jwt__SecretKey="your-super-secret-jwt-key-here" \
  --from-literal=DB_PASSWORD="yourpassword" \
  --from-literal=EXTERNAL_API_KEY="your-external-api-key"
```

### 2. Configurar GitHub Secrets
No seu repositório GitHub, vá em Settings > Secrets and variables > Actions e adicione:

- `GCP_PROJECT_ID`: ID do seu projeto GCP
- `GCP_SA_KEY`: JSON da service account (veja seção abaixo)

### 3. Criar Service Account para GitHub Actions
```bash
# Criar service account
gcloud iam service-accounts create github-actions \
    --description="Service account for GitHub Actions" \
    --display-name="GitHub Actions"

# Adicionar roles necessárias
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/container.developer"

gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.admin"

# Gerar chave JSON
gcloud iam service-accounts keys create github-actions-key.json \
    --iam-account=github-actions@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

## Deploy Manual

### 1. Build e push da imagem
```bash
# Build da imagem
docker build -t gcr.io/YOUR_PROJECT_ID/authentication-service:latest .

# Push para GCR
docker push gcr.io/YOUR_PROJECT_ID/authentication-service:latest
```

### 2. Deploy usando Kustomize
```bash
# Atualizar PROJECT_ID no kustomization.yaml
sed -i 's/PROJECT_ID/YOUR_PROJECT_ID/g' kustomization.yaml

# Aplicar configurações
kubectl apply -k .
```

### 3. Verificar deploy
```bash
kubectl get pods -n authentication-service
kubectl get services -n authentication-service
kubectl logs -f deployment/authentication-service -n authentication-service
```

## Deploy com Skaffold (Desenvolvimento)

### 1. Configurar Skaffold
```bash
# Atualizar PROJECT_ID no skaffold.yaml
sed -i 's/PROJECT_ID/YOUR_PROJECT_ID/g' skaffold.yaml
```

### 2. Deploy local
```bash
skaffold dev --profile=local
```

### 3. Deploy em ambiente de desenvolvimento
```bash
skaffold run --profile=dev
```

## Configuração de Domínio e SSL

### 1. Reservar IP estático
```bash
gcloud compute addresses create authentication-service-ip --global
```

### 2. Configurar DNS
Aponte seu domínio para o IP reservado:
```bash
gcloud compute addresses describe authentication-service-ip --global
```

### 3. Atualizar Ingress
Edite `k8s/base/ingress.yaml` e substitua `auth.yourdomain.com` pelo seu domínio.

## Monitoramento e Logs

### 1. Verificar health checks
```bash
kubectl get pods -n authentication-service
kubectl describe pod POD_NAME -n authentication-service
```

### 2. Ver logs
```bash
kubectl logs -f deployment/authentication-service -n authentication-service
```

### 3. Verificar HPA
```bash
kubectl get hpa -n authentication-service
```

## Troubleshooting

### Problemas comuns

1. **Pod não inicia**
   - Verificar logs: `kubectl logs POD_NAME -n authentication-service`
   - Verificar secrets: `kubectl get secrets -n authentication-service`

2. **Erro de conexão com banco**
   - Verificar connection string no secret
   - Verificar se o banco está acessível do cluster

3. **Erro de autenticação no GCR**
   - Verificar se a service account tem as permissões corretas
   - Verificar se o secret GCP_SA_KEY está configurado corretamente

### Comandos úteis
```bash
# Reiniciar deployment
kubectl rollout restart deployment/authentication-service -n authentication-service

# Escalar manualmente
kubectl scale deployment authentication-service --replicas=5 -n authentication-service

# Port forward para teste local
kubectl port-forward service/authentication-service 8080:80 -n authentication-service
```

## Estrutura de Arquivos

```
.
├── .github/workflows/deploy.yml    # GitHub Actions workflow
├── k8s/
│   ├── base/                       # Configurações base do Kubernetes
│   │   ├── namespace.yaml
│   │   ├── configmap.yaml
│   │   ├── secret.yaml
│   │   ├── deployment.yaml
│   │   ├── service.yaml
│   │   ├── ingress.yaml
│   │   ├── hpa.yaml
│   │   └── kustomization.yaml
│   └── overlays/                   # Configurações específicas por ambiente
│       └── dev/
├── kustomization.yaml              # Kustomize principal
├── skaffold.yaml                   # Configuração do Skaffold
└── DEPLOYMENT.md                   # Este arquivo
```
