using HighCapital.AuthenticationService.Domain.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace HighCapital.AuthenticationService.Infrastructure.Data.Configurations;

public static class BaseEntityConfiguration
{
    public static void ConfigureBaseEntity<T>(this EntityTypeBuilder<T> builder) where T : BaseEntity
    {
        // Ignore DomainEvents property in all entities
        builder.Ignore(e => e.DomainEvents);
    }
}
