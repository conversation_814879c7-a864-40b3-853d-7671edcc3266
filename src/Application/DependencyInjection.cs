using System.Reflection;
using HighCapital.AuthenticationService.Application.Common.Interfaces;
using HighCapital.AuthenticationService.Application.Services;
using Microsoft.Extensions.Hosting;
using FluentValidation;

namespace Microsoft.Extensions.DependencyInjection;

public static class DependencyInjection
{
    public static void AddApplicationServices(this IHostApplicationBuilder builder)
    {
        builder.Services.AddAutoMapper(Assembly.GetExecutingAssembly());
        builder.Services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());
        
        // Register Application Services
        builder.Services.AddScoped<IUserService, UserService>();
        // Add more services as needed
    }
}
