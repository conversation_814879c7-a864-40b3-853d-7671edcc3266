// Adicione seus value objects aqui
// Exemplo:
// public class Money : ValueObject
// {
//     public decimal Amount { get; private set; }
//     public string Currency { get; private set; }
//     
//     public Money(decimal amount, string currency)
//     {
//         Amount = amount;
//         Currency = currency;
//     }
//     
//     protected override IEnumerable<object> GetEqualityComponents()
//     {
//         yield return Amount;
//         yield return Currency;
//     }
// }
