using HighCapital.AuthenticationService.Application.Common.Interfaces;
using HighCapital.AuthenticationService.Application.Validators;
using Microsoft.AspNetCore.Mvc;
using FluentValidation;

namespace HighCapital.AuthenticationService.Service.Controllers.Auth;

[ApiController]
[Route("api/v1/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly IValidator<RegisterUserRequest> _registerValidator;
    private readonly IValidator<LoginUserRequest> _loginValidator;

    public AuthController(
        IUserService userService,
        IValidator<RegisterUserRequest> registerValidator,
        IValidator<LoginUserRequest> loginValidator)
    {
        _userService = userService;
        _registerValidator = registerValidator;
        _loginValidator = loginValidator;
    }

    [HttpPost("register")]
    public async Task<ActionResult> Register([FromBody] RegisterUserRequest request)
    {
        var validationResult = await _registerValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(new { errors = validationResult.Errors.Select(e => e.ErrorMessage) });
        }

        var result = await _userService.RegisterUserAsync(
            request.FirstName,
            request.LastName,
            request.Email,
            request.Password);

        if (!result.Succeeded)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new { message = "User registered successfully", userId = result.Value });
    }

    [HttpPost("login")]
    public async Task<ActionResult> Login([FromBody] LoginUserRequest request)
    {
        var validationResult = await _loginValidator.ValidateAsync(request);
        if (!validationResult.IsValid)
        {
            return BadRequest(new { errors = validationResult.Errors.Select(e => e.ErrorMessage) });
        }

        var result = await _userService.LoginUserAsync(request.Email, request.Password);

        if (!result.Succeeded || result.Value == null)
        {
            return BadRequest(new { errors = result.Errors });
        }

        return Ok(new { token = result.Value });
    }
}
