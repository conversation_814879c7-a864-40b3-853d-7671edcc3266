apiVersion: skaffold/v4beta7
kind: Config
metadata:
  name: authentication-service

build:
  artifacts:
  - image: gcr.io/PROJECT_ID/authentication-service
    docker:
      dockerfile: Dockerfile
  googleCloudBuild:
    projectId: PROJECT_ID
    diskSizeGb: 100
    machineType: E2_HIGHCPU_8
    timeout: 1200s

deploy:
  kustomize:
    paths:
    - .

profiles:
- name: local
  build:
    local:
      push: false
  deploy:
    kustomize:
      paths:
      - k8s/overlays/local
  portForward:
  - resourceType: service
    resourceName: authentication-service
    port: 80
    localPort: 8080

- name: dev
  build:
    googleCloudBuild:
      projectId: PROJECT_ID
  deploy:
    kustomize:
      paths:
      - k8s/overlays/dev

- name: staging
  build:
    googleCloudBuild:
      projectId: PROJECT_ID
  deploy:
    kustomize:
      paths:
      - k8s/overlays/staging

- name: prod
  build:
    googleCloudBuild:
      projectId: PROJECT_ID
  deploy:
    kustomize:
      paths:
      - k8s/overlays/prod
