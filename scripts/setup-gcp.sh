#!/bin/bash

# Setup script for GCP deployment
# Usage: ./scripts/setup-gcp.sh YOUR_PROJECT_ID

set -e

PROJECT_ID=$1
CLUSTER_NAME="authentication-service-cluster"
ZONE="us-central1-a"
SERVICE_ACCOUNT_NAME="github-actions"

if [ -z "$PROJECT_ID" ]; then
    echo "Usage: $0 PROJECT_ID"
    echo "Example: $0 my-project-123"
    exit 1
fi

echo "🚀 Setting up GCP for project: $PROJECT_ID"

# Set project
echo "📋 Setting project..."
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable container.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable compute.googleapis.com

# Create GKE cluster
echo "🏗️  Creating GKE cluster..."
if ! gcloud container clusters describe $CLUSTER_NAME --zone=$ZONE &>/dev/null; then
    gcloud container clusters create $CLUSTER_NAME \
        --zone=$ZONE \
        --num-nodes=3 \
        --enable-autoscaling \
        --min-nodes=1 \
        --max-nodes=10 \
        --machine-type=e2-medium \
        --enable-autorepair \
        --enable-autoupgrade \
        --enable-ip-alias \
        --network=default \
        --subnetwork=default
else
    echo "Cluster $CLUSTER_NAME already exists"
fi

# Get cluster credentials
echo "🔑 Getting cluster credentials..."
gcloud container clusters get-credentials $CLUSTER_NAME --zone=$ZONE

# Create service account for GitHub Actions
echo "👤 Creating service account for GitHub Actions..."
if ! gcloud iam service-accounts describe $SERVICE_ACCOUNT_NAME@$PROJECT_ID.iam.gserviceaccount.com &>/dev/null; then
    gcloud iam service-accounts create $SERVICE_ACCOUNT_NAME \
        --description="Service account for GitHub Actions" \
        --display-name="GitHub Actions"
else
    echo "Service account $SERVICE_ACCOUNT_NAME already exists"
fi

# Add IAM roles
echo "🔐 Adding IAM roles..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$SERVICE_ACCOUNT_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/container.developer"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$SERVICE_ACCOUNT_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.admin"

gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$SERVICE_ACCOUNT_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/cloudbuild.builds.editor"

# Generate service account key
echo "🗝️  Generating service account key..."
if [ ! -f "github-actions-key.json" ]; then
    gcloud iam service-accounts keys create github-actions-key.json \
        --iam-account=$SERVICE_ACCOUNT_NAME@$PROJECT_ID.iam.gserviceaccount.com
    echo "⚠️  Service account key saved to github-actions-key.json"
    echo "⚠️  Add this file content to GitHub Secrets as GCP_SA_KEY"
else
    echo "Service account key already exists"
fi

# Reserve static IP
echo "🌐 Reserving static IP..."
if ! gcloud compute addresses describe authentication-service-ip --global &>/dev/null; then
    gcloud compute addresses create authentication-service-ip --global
else
    echo "Static IP authentication-service-ip already exists"
fi

# Get static IP
STATIC_IP=$(gcloud compute addresses describe authentication-service-ip --global --format="value(address)")
echo "📍 Static IP: $STATIC_IP"

# Update configuration files
echo "📝 Updating configuration files..."
sed -i.bak "s/PROJECT_ID/$PROJECT_ID/g" kustomization.yaml
sed -i.bak "s/PROJECT_ID/$PROJECT_ID/g" skaffold.yaml
sed -i.bak "s/PROJECT_ID/$PROJECT_ID/g" .github/workflows/deploy.yml

echo "✅ Setup completed!"
echo ""
echo "Next steps:"
echo "1. Add the following secrets to your GitHub repository:"
echo "   - GCP_PROJECT_ID: $PROJECT_ID"
echo "   - GCP_SA_KEY: (content of github-actions-key.json)"
echo ""
echo "2. Update your domain DNS to point to: $STATIC_IP"
echo ""
echo "3. Update the domain in k8s/base/ingress.yaml"
echo ""
echo "4. Create Kubernetes secrets:"
echo "   kubectl create namespace authentication-service"
echo "   kubectl create secret generic authentication-service-secret \\"
echo "     --namespace=authentication-service \\"
echo "     --from-literal=ConnectionStrings__DefaultConnection=\"your-connection-string\" \\"
echo "     --from-literal=Jwt__SecretKey=\"your-jwt-secret\""
