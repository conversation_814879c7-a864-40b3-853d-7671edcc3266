#!/bin/bash

# Manual deployment script
# Usage: ./scripts/deploy.sh [environment]

set -e

ENVIRONMENT=${1:-prod}
PROJECT_ID=$(gcloud config get-value project)
IMAGE_TAG=${2:-latest}

if [ -z "$PROJECT_ID" ]; then
    echo "❌ No GCP project configured. Run 'gcloud config set project YOUR_PROJECT_ID'"
    exit 1
fi

echo "🚀 Deploying authentication-service to $ENVIRONMENT environment"
echo "📋 Project: $PROJECT_ID"
echo "🏷️  Image tag: $IMAGE_TAG"

# Build and push image
echo "🔨 Building Docker image..."
docker build -t gcr.io/$PROJECT_ID/authentication-service:$IMAGE_TAG .

echo "📤 Pushing image to GCR..."
docker push gcr.io/$PROJECT_ID/authentication-service:$IMAGE_TAG

# Update image tag in kustomization
echo "📝 Updating image tag..."
if [ -f "kustomization.yaml.bak" ]; then
    cp kustomization.yaml.bak kustomization.yaml
fi

sed -i.bak "s/newTag: TAG/newTag: $IMAGE_TAG/g" kustomization.yaml

# Deploy to Kubernetes
echo "🚢 Deploying to Kubernetes..."
if [ "$ENVIRONMENT" = "dev" ]; then
    kubectl apply -k k8s/overlays/dev
else
    kubectl apply -k .
fi

# Wait for rollout
echo "⏳ Waiting for deployment to complete..."
kubectl rollout status deployment/authentication-service -n authentication-service --timeout=300s

# Show status
echo "📊 Deployment status:"
kubectl get pods -n authentication-service
kubectl get services -n authentication-service

echo "✅ Deployment completed successfully!"

# Show logs
echo "📋 Recent logs:"
kubectl logs -l app=authentication-service -n authentication-service --tail=10
